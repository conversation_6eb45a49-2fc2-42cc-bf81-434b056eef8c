import { Router, Request, Response } from "express";
import SystemSettings from "../../../models/systemSettings";
import { purgeDocument } from "../../../utils/files/purgeDocument";
import { getVectorDbClass } from "../../../utils/helpers";
import {
  exportChatsAsType,
  ExportFormatType,
  ChatType,
  ChatFilters,
} from "../../../utils/helpers/chat/convertTo";
import { dumpENV, updateENV } from "../../../utils/helpers/updateENV";
import { reqBody } from "../../../utils/http";
import { validApiKey } from "../../../utils/middleware/validApiKey";
import * as path from "path";
import * as fs from "fs";
import { handleFileUpload } from "../../../utils/files/multer";
import { v4 } from "uuid";
import { validatedRequest } from "../../../utils/middleware/validatedRequest";
import {
  SystemSettings as SystemSettingsType,
  SystemSettingsResponse,
  VectorCountResponse,
  UpdateEnvResponse,
  RemoveDocumentsRequest,
  RemoveDocumentsResponse,
  ProcessAttachmentResponse,
} from "../../../types/api";

import { rexorProxyEndpoints } from "./rexorProxy";

export function apiSystemEndpoints(app: Router): void {
  if (!app) return;

  rexorProxyEndpoints(app);

  app.get(
    "/v1/system/env-dump",
    async (_req: Request, response: Response): Promise<void> => {
      /*
   #swagger.tags = ['System Settings']
   #swagger.description = 'Dump all settings to file storage'
   #swagger.responses[403] = {
     schema: {
       "$ref": "#/definitions/InvalidAPIKey"
     }
   }
   */
      try {
        if (process.env.NODE_ENV !== "production") {
          response
            .status(200)
            .json({ success: true, message: "Environment dump completed" });
          return;
        }
        dumpENV();
        response
          .status(200)
          .json({ success: true, message: "Environment dump completed" });
      } catch (e) {
        console.log((e as Error).message, e);
        response.sendStatus(500);
        return;
      }
    }
  );

  // Add env-dump endpoint without v1 prefix for API compatibility
  app.get(
    "/env-dump",
    async (_req: Request, response: Response): Promise<void> => {
      try {
        if (process.env.NODE_ENV !== "production") {
          response
            .status(200)
            .json({ success: true, message: "Environment dump completed" });
          return;
        }
        dumpENV();
        response
          .status(200)
          .json({ success: true, message: "Environment dump completed" });
      } catch (e) {
        console.log((e as Error).message, e);
        response.sendStatus(500);
        return;
      }
    }
  );

  app.get(
    "/v1/system",
    [validApiKey],
    async (_req: Request, response: Response): Promise<void> => {
      /*
    #swagger.tags = ['System Settings']
    #swagger.description = 'Get all current system settings that are defined.'
    #swagger.responses[200] = {
      content: {
        "application/json": {
          schema: {
            type: 'object',
            example: {
             "settings": {
                "VectorDB": "pinecone",
                "PineConeKey": true,
                "PineConeIndex": "my-pinecone-index",
                "LLMProvider": "azure",
                "[KEY_NAME]": "KEY_VALUE",
              }
            }
          }
        }
      }
    }
    #swagger.responses[403] = {
      schema: {
        "$ref": "#/definitions/InvalidAPIKey"
      }
    }
    */
      try {
        const rawSettings = await SystemSettings.currentSettings();
        // Filter out undefined values to match SystemSettings type
        const settings = Object.fromEntries(
          Object.entries(rawSettings).filter(
            ([_key, value]) => value !== undefined
          )
        ) as SystemSettingsType;
        const settingsResponse: SystemSettingsResponse = { settings };
        response.status(200).json(settingsResponse);
      } catch (e) {
        console.log((e as Error).message, e);
        response.sendStatus(500);
        return;
      }
    }
  );

  app.get(
    "/v1/system/vector-count",
    [validApiKey],
    async (_req: Request, response: Response): Promise<void> => {
      /*
    #swagger.tags = ['System Settings']
    #swagger.description = 'Number of all vectors in connected vector database'
    #swagger.responses[200] = {
      content: {
        "application/json": {
          schema: {
            type: 'object',
            example: {
             "vectorCount": 5450
            }
          }
        }
      }
    }
    #swagger.responses[403] = {
      schema: {
        "$ref": "#/definitions/InvalidAPIKey"
      }
    }
    */
      try {
        const VectorDb = getVectorDbClass();
        const vectorCount = await VectorDb.totalVectors();
        const vectorCountResponse: VectorCountResponse = { vectorCount };
        response.status(200).json(vectorCountResponse);
      } catch (e) {
        console.log((e as Error).message, e);
        response.sendStatus(500);
        return;
      }
    }
  );

  // Add system/vector-count endpoint without v1 prefix for API compatibility
  app.get(
    "/system/vector-count",
    [validatedRequest],
    async (_req: Request, response: Response): Promise<void> => {
      try {
        const VectorDb = getVectorDbClass();
        const vectorCount = await VectorDb.totalVectors();
        const vectorCountResponse: VectorCountResponse = { vectorCount };
        response.status(200).json(vectorCountResponse);
      } catch (e) {
        console.log((e as Error).message, e);
        response.sendStatus(500);
        return;
      }
    }
  );

  app.post(
    "/v1/system/update-env",
    [validApiKey],
    async (request: Request, response: Response): Promise<void> => {
      /*
      #swagger.tags = ['System Settings']
      #swagger.description = 'Update a system setting or preference.'
      #swagger.requestBody = {
        description: 'Key pair object that matches a valid setting and value. Get keys from GET /v1/system or refer to codebase.',
        required: true,
        content: {
          "application/json": {
            example: {
              VectorDB: "lancedb",
              AnotherKey: "updatedValue"
            }
          }
        }
      }
      #swagger.responses[200] = {
        content: {
          "application/json": {
            schema: {
              type: 'object',
              example: {
                newValues: {"[ENV_KEY]": 'Value'},
                error: 'error goes here, otherwise null'
              }
            }
          }
        }
      }
      #swagger.responses[403] = {
        schema: {
          "$ref": "#/definitions/InvalidAPIKey"
        }
      }
      */
      try {
        const body = reqBody(request);
        const { newValues, error } = await updateENV(
          body as Record<
            string,
            string | number | boolean | Record<string, string> | undefined
          >
        );
        const updateResponse: UpdateEnvResponse = {
          newValues,
          error: error === false || error === null ? null : error,
        };
        response.status(200).json(updateResponse);
      } catch (e) {
        console.log((e as Error).message, e);
        response.sendStatus(500);
        return;
      }
    }
  );

  app.get(
    "/v1/system/export-chats",
    [validatedRequest],
    async (request: Request, response: Response): Promise<void> => {
      try {
        const {
          type = "jsonl",
          chatType = "workspace",
          filters = "{}",
        } = request.query as {
          type?: string;
          chatType?: string;
          filters?: string;
        };

        // Ensure filters is a string before parsing
        const filtersString: string =
          typeof filters !== "string" ? JSON.stringify(filters) : filters;

        // Parse filters safely
        let parsedFilters: ChatFilters;
        try {
          parsedFilters = JSON.parse(filtersString) as ChatFilters;
        } catch (err) {
          console.error("Error parsing filters:", err);
          response.status(400).json({
            error: "Invalid filters format",
            details: "The filters parameter must be valid JSON",
          });
          return;
        }

        const { contentType, data } = await exportChatsAsType(
          type as ExportFormatType,
          chatType as ChatType,
          parsedFilters
        );

        if (!data) {
          response.status(404).json({
            error: "No data available for export",
            details: "No chats found matching the specified criteria",
          });
          return;
        }

        response.setHeader("Content-Type", contentType);
        response.setHeader(
          "Content-Disposition",
          `attachment; filename=chat_export.${type}`
        );
        response.send(data);
      } catch (error) {
        console.error("Error in /export-chats:", error);
        response.status(500).json({
          error: "Export failed",
          details:
            "Failed to export chats. Please try again or contact support if the issue persists.",
        });
      }
    }
  );

  app.delete(
    "/v1/system/remove-documents",
    [validApiKey],
    async (request: Request, response: Response): Promise<void> => {
      /*
      #swagger.tags = ['System Settings']
      #swagger.description = 'Permanently remove documents from the system.'
      #swagger.requestBody = {
        description: 'Array of document names to be removed permanently.',
        required: true,
        content: {
          "application/json": {
            schema: {
              type: 'object',
              properties: {
                names: {
                  type: 'array',
                  items: {
                    type: 'string'
                  },
                  example: [
                    "custom-documents/file.txt-fc4beeeb-e436-454d-8bb4-e5b8979cb48f.json"
                  ]
                }
              }
            }
          }
        }
      }
      #swagger.responses[200] = {
        description: 'Documents removed successfully.',
        content: {
          "application/json": {
            schema: {
              type: 'object',
              example: {
                success: true,
                message: 'Documents removed successfully'
              }
            }
          }
        }
      }
      #swagger.responses[403] = {
        description: 'Forbidden',
        schema: {
          "$ref": "#/definitions/InvalidAPIKey"
        }
      }
      #swagger.responses[500] = {
        description: 'Internal Server Error'
      }
      */
      try {
        const { names } = reqBody(request) as RemoveDocumentsRequest;
        for await (const name of names) await purgeDocument(name);
        const removeResponse: RemoveDocumentsResponse = {
          success: true,
          message: "Documents removed successfully",
        };
        response.status(200).json(removeResponse);
      } catch (e) {
        console.log((e as Error).message, e);
        response.sendStatus(500);
        return;
      }
    }
  );

  app.post(
    "/system/process-attachment",
    handleFileUpload,
    async (
      request: Request & { file?: Express.Multer.File },
      response: Response
    ): Promise<void> => {
      try {
        if (!request.file) {
          const errorResponse: ProcessAttachmentResponse = {
            success: false,
            message: "No file provided",
          };
          response.status(400).json(errorResponse);
          return;
        }

        const { originalname, path: tempPath, mimetype } = request.file;

        // Get workspace root directory
        const workspaceRoot = path.resolve(__dirname, "../../../..");

        // Define paths
        const attachmentsPath = path.join(
          workspaceRoot,
          "server/storage/documents/attachments"
        );
        const hotdirPath = path.join(workspaceRoot, "frontend/public/hotdir");

        // Ensure directories exist
        if (!fs.existsSync(attachmentsPath)) {
          fs.mkdirSync(attachmentsPath, { recursive: true });
        }
        if (!fs.existsSync(hotdirPath)) {
          fs.mkdirSync(hotdirPath, { recursive: true });
        }

        // Generate unique filename
        const uniqueId = v4();
        const fileExt = path.extname(originalname);
        const uniqueFilename = `${path.basename(originalname, fileExt)}-${uniqueId}${fileExt}`;

        // Copy file to both locations
        const attachmentPath = path.join(attachmentsPath, uniqueFilename);
        const hotdirFilePath = path.join(hotdirPath, uniqueFilename);

        fs.copyFileSync(tempPath, attachmentPath);
        fs.copyFileSync(tempPath, hotdirFilePath);

        // Clean up temp file
        fs.unlinkSync(tempPath);

        const successResponse: ProcessAttachmentResponse = {
          success: true,
          document: {
            id: uniqueId,
            location: uniqueFilename,
            name: originalname,
            type: mimetype,
          },
        };
        response.status(200).json(successResponse);
      } catch (error) {
        console.error("Error processing attachment:", error);
        const errorResponse: ProcessAttachmentResponse = {
          success: false,
          message: (error as Error).message || "Failed to process attachment",
        };
        response.status(500).json(errorResponse);
      }
    }
  );

  // Add admin/system-settings endpoint for API compatibility
  app.get(
    "/admin/system-settings",
    [validatedRequest],
    async (_req: Request, response: Response): Promise<void> => {
      try {
        const rawSettings = await SystemSettings.currentSettings();
        // Filter out undefined values to match SystemSettings type
        const settings = Object.fromEntries(
          Object.entries(rawSettings).filter(
            ([_key, value]) => value !== undefined
          )
        ) as SystemSettingsType;
        const settingsResponse: SystemSettingsResponse = { settings };
        response.status(200).json(settingsResponse);
      } catch (e) {
        console.log((e as Error).message, e);
        response.sendStatus(500);
        return;
      }
    }
  );
}
