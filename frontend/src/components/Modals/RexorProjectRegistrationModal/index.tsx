import React, { useState, useEffect, FormEvent, ChangeEvent } from "react";
import RexorLoginModal from "../RexorLoginModal";
import {
  useRexorIsLoggedIn,
  useRexorLogout,
  useRexorRegisterProject,
  useRexorCheckLoginStatus,
  useRexorSetRegisteredProject,
  useRexorToken,
} from "@/stores/rexorStore";
import { useTranslation } from "react-i18next";
import Modal from "@/components/ui/Modal";
import { Button } from "@/components/Button";
import { MdOutlineManageAccounts } from "react-icons/md";
import { Tooltip } from "react-tooltip";
import useUser from "@/hooks/useUser";

interface RexorProjectRegistrationProps {
  isOpen: boolean;
  onClose?: () => void;
  onSelect?: (projectResponse: any) => void;
  canClose?: boolean;
}

interface ProjectData {
  ProjectID: string;
  ResourceID: string;
}

interface ProjectResponse {
  ProjectStatus: number;
  ProjectUID: string;
  ResourceUID: string;
  ProjectActivityUID: string;
  ArticleUID: string;
  ResourceID: string;
  ArticleID: string;
}

interface ProjectDetails {
  ProjectUID: string;
  ResourceUID: string;
  ProjectActivityUID: string;
  CompensationTypeUID: string;
  ResourceID: string;
  ArticleID: string;
}

export default function RexorProjectRegistration({
  isOpen,
  onClose,
  onSelect,
  canClose = true,
}: RexorProjectRegistrationProps): React.ReactElement {
  const [showLoginModal, setShowLoginModal] = useState<boolean>(false);
  const { t } = useTranslation();
  const { user } = useUser();
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");

  const isLoggedIn = useRexorIsLoggedIn();
  const logout = useRexorLogout();
  const registerNewProject = useRexorRegisterProject();
  const checkLoginStatus = useRexorCheckLoginStatus();
  const setRegisteredProject = useRexorSetRegisteredProject();
  const token = useRexorToken();

  const [projectData, setProjectData] = useState<ProjectData>({
    ProjectID: "",
    ResourceID: "",
  });

  useEffect(() => {
    if (user?.economy_system_id) {
      setProjectData((prev) => ({
        ...prev,
        ResourceID: String(user.economy_system_id),
      }));
    }
  }, [user]);

  console.log("User", user);
  const handleInputChange = (e: ChangeEvent<HTMLInputElement>): void => {
    setError("");
    setProjectData((prev) => ({
      ...prev,
      ProjectID: e.target.value,
    }));
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();
    if (!projectData.ProjectID) {
      return;
    }
    await registerProject();
  };

  const registerProject = async (): Promise<void> => {
    setLoading(true);
    setError("");
    try {
      if (!token) {
        throw new Error(t("rexor.no-token"));
      }
      const projectResponse = (await registerNewProject(
        projectData
      )) as ProjectResponse[];
      if (projectResponse && projectResponse.length > 0) {
        if (projectResponse[0].ProjectStatus === 20) {
          const projectDetails: ProjectDetails = {
            ProjectUID: projectResponse[0].ProjectUID,
            ResourceUID: projectResponse[0].ResourceUID,
            ProjectActivityUID: projectResponse[0].ProjectActivityUID,
            CompensationTypeUID: projectResponse[0].ArticleUID,
            ResourceID: projectResponse[0].ResourceID,
            ArticleID: projectResponse[0].ArticleID,
          };

          setRegisteredProject(projectDetails);

          onSelect?.(projectResponse);
        } else {
          setRegisteredProject({} as ProjectDetails);
          setError(t("rexor.not-active"));
        }
      } else {
        setError(t("rexor.not-exist"));
      }
    } catch (err: any) {
      // Check for 401 authentication errors
      if (
        err.status === 401 ||
        err.message?.includes("Authentication failed")
      ) {
        logout();
        setShowLoginModal(true);
        setError(t("rexor.session-expired"));
      } else if (
        err.message?.includes("Invalid or expired token") ||
        err.message?.includes("Unauthorized")
      ) {
        logout();
        setShowLoginModal(true);
        setError(t("rexor.session-expired"));
      } else {
        setError(err.message || t("rexor.error"));
      }
    } finally {
      setLoading(false);
    }
  };

  const handleLoginSuccess = (token: string): void => {
    if (!token) {
      setError(t("rexor.account.no-token"));
      return;
    }
    setShowLoginModal(false);
    setError("");
    checkLoginStatus();
  };

  const handleChangeAccount = (): void => {
    setShowLoginModal(true);
  };

  const handleRexorLogout = (): void => {
    setRegisteredProject({} as ProjectDetails);
    checkLoginStatus();
    onClose?.();
  };

  useEffect(() => {
    if (isOpen && isLoggedIn) {
      setError("");
    } else if (isOpen && !isLoggedIn) {
      setShowLoginModal(true);
    }
  }, [isLoggedIn, isOpen]);

  const getMissingEconomyIdMessage = (): React.ReactElement | null => {
    if (!user?.economy_system_id) {
      if (user?.role === "admin" || user?.role === "manager") {
        return (
          <div className="text-yellow-500 text-sm mb-4 p-2 border border-yellow-500 rounded-md bg-yellow-500 bg-opacity-10">
            {t("rexor.missing-economy-id-admin")}
          </div>
        );
      } else {
        return (
          <div className="text-yellow-500 text-sm mb-4 p-2 border border-yellow-500 rounded-md bg-yellow-500 bg-opacity-10">
            {t("rexor.missing-economy-id-user")}
          </div>
        );
      }
    }
    return null;
  };

  const renderContent = (): React.ReactElement => {
    if (!isLoggedIn) {
      return (
        <div className="text-center">
          <p>{t("rexor.please-login")}</p>
          <Button onClick={() => setShowLoginModal(true)}>
            {t("rexor.login")}
          </Button>
        </div>
      );
    }

    return (
      <>
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">{t("rexor.register")}</h3>
            <div className="flex item-center justify-center space-x-2">
              {isLoggedIn && (
                <>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleChangeAccount}
                    data-tooltip-id="change-account"
                    data-tooltip-content={t("rexor.account.change-account")}
                    aria-label={t("rexor.change-account")}
                  >
                    <MdOutlineManageAccounts />
                  </Button>
                  <Tooltip
                    id="change-account"
                    delayShow={300}
                    className="tooltip"
                  />
                </>
              )}
            </div>
          </div>

          {getMissingEconomyIdMessage()}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">
                {t("rexor.project-id")}
              </label>
              <input
                type="text"
                name="ProjectID"
                value={projectData.ProjectID}
                onChange={handleInputChange}
                className="dark-input-mdl w-full text-foreground text-sm rounded-md block p-2"
                placeholder="Project ID"
                required
              />
            </div>

            {error && <span className="text-red-500 text-sm">{error}</span>}

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onClose}>
                {t("button.cancel")}
              </Button>
              <Button
                type="submit"
                disabled={loading || !user?.economy_system_id}
              >
                {loading ? t("rexor.registering") : t("rexor.register")}
              </Button>
            </div>
          </form>
        </div>
      </>
    );
  };

  return (
    <>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        title={t("rexor.register-project")}
        preventClose={!canClose}
      >
        <div className="space-y-6">
          <div className="transition-all duration-300 ease-in-out">
            {renderContent()}
          </div>
        </div>
      </Modal>

      <RexorLoginModal
        show={showLoginModal}
        onClose={() => setShowLoginModal(false)}
        onSuccess={handleLoginSuccess}
        onLogout={handleRexorLogout}
      />
    </>
  );
}
